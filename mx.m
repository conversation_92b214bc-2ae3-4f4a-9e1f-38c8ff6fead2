function out = model
%
% mx.m
%
% Model exported on Jul 30 2025, 14:14 by COMSOL 6.3.0.290.

import com.comsol.model.*
import com.comsol.model.util.*

model = ModelUtil.create('Model');

model.modelPath('D:\Code\optimization_algorithm\opimization_surrogate');

model.label('mx2.mph');

model.component.create('comp1', true);

model.component('comp1').geom.create('geom1', 2);

model.result.table.create('evl2', 'Table');
model.result.table.create('tbl1', 'Table');

model.component('comp1').func.create('int1', 'Interpolation');
model.component('comp1').func.create('int2', 'Interpolation');
model.component('comp1').func('int1').set('source', 'file');
model.component('comp1').func('int1').set('filename', 'D:\Code\optimization_algorithm\opimization_surrogate\x.txt');
model.component('comp1').func('int1').set('nargs', 2);
model.component('comp1').func('int1').set('extrap', 'linear');
model.component('comp1').func('int1').set('argunit', {'mm' 'mm'});
model.component('comp1').func('int2').set('source', 'file');
model.component('comp1').func('int2').set('filename', 'D:\Code\optimization_algorithm\opimization_surrogate\x.txt');
model.component('comp1').func('int2').set('nargs', 2);
model.component('comp1').func('int2').set('extrap', 'linear');
model.component('comp1').func('int2').set('argunit', {'mm' 'mm'});

model.component('comp1').mesh.create('mesh1');

model.component('comp1').geom('geom1').lengthUnit('mm');
model.component('comp1').geom('geom1').create('r1', 'Rectangle');
model.component('comp1').geom('geom1').feature('r1').set('size', [75 100]);
model.component('comp1').geom('geom1').create('r2', 'Rectangle');
model.component('comp1').geom('geom1').feature('r2').set('size', [10 50]);
model.component('comp1').geom('geom1').create('mov1', 'Move');
model.component('comp1').geom('geom1').feature('mov1').setIndex('displx', '-10', 0);
model.component('comp1').geom('geom1').feature('mov1').setIndex('disply', '-60', 0);
model.component('comp1').geom('geom1').feature('mov1').selection('input').set({'r2'});
model.component('comp1').geom('geom1').create('r3', 'Rectangle');
model.component('comp1').geom('geom1').feature('r3').set('size', [50 10]);
model.component('comp1').geom('geom1').create('mov2', 'Move');
model.component('comp1').geom('geom1').feature('mov2').setIndex('displx', '-10', 0);
model.component('comp1').geom('geom1').feature('mov2').setIndex('disply', '-60', 0);
model.component('comp1').geom('geom1').feature('mov2').selection('input').set({'r3'});
model.component('comp1').geom('geom1').create('r4', 'Rectangle');
model.component('comp1').geom('geom1').feature('r4').set('size', [10 100]);
model.component('comp1').geom('geom1').create('mov3', 'Move');
model.component('comp1').geom('geom1').feature('mov3').setIndex('displx', '-10', 0);
model.component('comp1').geom('geom1').feature('mov3').selection('input').set({'r4'});
model.component('comp1').geom('geom1').create('r5', 'Rectangle');
model.component('comp1').geom('geom1').feature('r5').set('size', [85 10]);
model.component('comp1').geom('geom1').create('mov4', 'Move');
model.component('comp1').geom('geom1').feature('mov4').setIndex('displx', '-10', 0);
model.component('comp1').geom('geom1').feature('mov4').setIndex('disply', '-10', 0);
model.component('comp1').geom('geom1').feature('mov4').selection('input').set({'r5'});
model.component('comp1').geom('geom1').create('r6', 'Rectangle');
model.component('comp1').geom('geom1').feature('r6').set('pos', [-10 100]);
model.component('comp1').geom('geom1').feature('r6').set('size', [85 10]);
model.component('comp1').geom('geom1').create('uni1', 'Union');
model.component('comp1').geom('geom1').feature('uni1').set('intbnd', false);
model.component('comp1').geom('geom1').feature('uni1').selection('input').set({'mov1' 'mov2' 'mov3' 'mov4' 'r6'});
model.component('comp1').geom('geom1').create('pare1', 'PartitionEdges');
model.component('comp1').geom('geom1').feature('pare1').set('position', 'projection');
model.component('comp1').geom('geom1').feature('pare1').selection('edge').set('uni1(1)', 7);
model.component('comp1').geom('geom1').feature('pare1').selection('vertexproj').set('uni1(1)', 11);
model.component('comp1').geom('geom1').run;

model.component('comp1').variable.create('var1');
model.component('comp1').variable('var1').set('aa', '10');

model.component('comp1').material.create('mat1', 'Common');
model.component('comp1').material.create('mat2', 'Common');
model.component('comp1').material('mat1').selection.set([2]);
model.component('comp1').material('mat2').selection.set([1]);

model.component('comp1').physics.create('solid', 'SolidMechanics', 'geom1');
model.component('comp1').physics('solid').create('hmm1', 'HyperelasticModel', 2);
model.component('comp1').physics('solid').feature('hmm1').selection.set([2]);
model.component('comp1').physics('solid').create('hmm2', 'HyperelasticModel', 2);
model.component('comp1').physics('solid').feature('hmm2').selection.set([1]);
model.component('comp1').physics('solid').create('fix1', 'Fixed', 1);
model.component('comp1').physics('solid').feature('fix1').selection.set([7]);
model.component('comp1').physics('solid').create('bndl1', 'BoundaryLoad', 1);
model.component('comp1').physics('solid').feature('bndl1').selection.set([12]);
model.component('comp1').physics('solid').create('bndl2', 'BoundaryLoad', 1);
model.component('comp1').physics('solid').feature('bndl2').selection.set([5 18]);
model.component('comp1').physics('solid').create('bndl3', 'BoundaryLoad', 1);
model.component('comp1').physics('solid').create('bndl4', 'BoundaryLoad', 1);
model.component('comp1').physics('solid').feature('bndl4').selection.set([15]);
model.component('comp1').physics('solid').create('sym1', 'SymmetrySolid', 1);
model.component('comp1').physics('solid').feature('sym1').selection.set([17 18 19]);
model.component('comp1').physics('solid').create('pl1', 'PointLoad', 0);
model.component('comp1').physics('solid').feature('pl1').selection.set([18]);

model.component('comp1').mesh('mesh1').create('fq1', 'FreeQuad');

model.component('comp1').probe.create('point1', 'Point');
model.component('comp1').probe('point1').selection.set([13]);

model.result.table('evl2').label('Evaluation 2D');
model.result.table('evl2').comments([native2unicode(hex2dec({'4e' 'a4'}), 'unicode')  native2unicode(hex2dec({'4e' '92'}), 'unicode')  native2unicode(hex2dec({'76' '84'}), 'unicode')  native2unicode(hex2dec({'4e' '8c'}), 'unicode')  native2unicode(hex2dec({'7e' 'f4'}), 'unicode')  native2unicode(hex2dec({'50' '3c'}), 'unicode') ]);
model.result.table('tbl1').label([native2unicode(hex2dec({'63' 'a2'}), 'unicode')  native2unicode(hex2dec({'94' '88'}), 'unicode')  native2unicode(hex2dec({'88' '68'}), 'unicode') ' 1']);

model.component('comp1').view('view1').axis.set('xmin', -127.18826293945312);
model.component('comp1').view('view1').axis.set('xmax', 244.39703369140625);
model.component('comp1').view('view1').axis.set('ymin', -111.10444641113281);
model.component('comp1').view('view1').axis.set('ymax', 238.06117248535156);

model.component('comp1').material('mat1').propertyGroup('def').set('density', 'int1(x,y)');
model.component('comp1').material('mat2').propertyGroup('def').set('density', '1');

model.component('comp1').physics('solid').prop('Type2D').set('Type2D', 'PlaneStress');
model.component('comp1').physics('solid').feature('hmm1').set('MaterialModel', 'Ogden');
model.component('comp1').physics('solid').feature('hmm1').set('Compressibility_Ogden', 'Incompressible');
model.component('comp1').physics('solid').feature('hmm1').set('alphap', [3.848; 0.663; 4.225]);
model.component('comp1').physics('solid').feature('hmm1').set('mup', {'1.887e3*int1(x,y)'; '2.225e4*int1(x,y)'; '3.574e3*int1(x,y)'});
model.component('comp1').physics('solid').feature('hmm2').set('MaterialModel', 'Ogden');
model.component('comp1').physics('solid').feature('hmm2').set('Compressibility_Ogden', 'Incompressible');
model.component('comp1').physics('solid').feature('hmm2').set('alphap', [3.848; 0.663; 4.225]);
model.component('comp1').physics('solid').feature('hmm2').set('mup', {'1.887e3'; '2.225e4'; '3.574e3'});
model.component('comp1').physics('solid').feature('bndl1').set('LoadType', 'ForceLength');
model.component('comp1').physics('solid').feature('bndl1').set('FperLength', {'aa'; '0'; '0'});
model.component('comp1').physics('solid').feature('bndl1').active(false);
model.component('comp1').physics('solid').feature('bndl2').set('LoadType', 'ForceLength');
model.component('comp1').physics('solid').feature('bndl2').set('FperLength', {'-aa'; '0'; '0'});
model.component('comp1').physics('solid').feature('bndl2').active(false);
model.component('comp1').physics('solid').feature('bndl3').set('LoadType', 'ForceLength');
model.component('comp1').physics('solid').feature('bndl3').set('FperLength', {'0'; 'aa'; '0'});
model.component('comp1').physics('solid').feature('bndl3').active(false);
model.component('comp1').physics('solid').feature('bndl4').set('LoadType', 'FollowerPressure');
model.component('comp1').physics('solid').feature('bndl4').set('FollowerPressure', 'aa');
model.component('comp1').physics('solid').feature('bndl4').set('FperLength', {'aa'; '0'; '0'});
model.component('comp1').physics('solid').feature('bndl4').active(false);
model.component('comp1').physics('solid').feature('pl1').set('Fp', {'0'; '-aa'; '0'});

model.component('comp1').mesh('mesh1').feature('size').set('hauto', 2);
model.component('comp1').mesh('mesh1').feature('size').set('custom', 'on');
model.component('comp1').mesh('mesh1').feature('size').set('hmax', 2);
model.component('comp1').mesh('mesh1').feature('size').set('hmin', 2);
model.component('comp1').mesh('mesh1').run;

model.component('comp1').probe('point1').set('type', 'minimum');
model.component('comp1').probe('point1').set('expr', 'u');
model.component('comp1').probe('point1').set('descr', [native2unicode(hex2dec({'4f' '4d'}), 'unicode')  native2unicode(hex2dec({'79' 'fb'}), 'unicode')  native2unicode(hex2dec({'57' '3a'}), 'unicode')  native2unicode(hex2dec({'ff' '0c'}), 'unicode') 'X ' native2unicode(hex2dec({'52' '06'}), 'unicode')  native2unicode(hex2dec({'91' 'cf'}), 'unicode') ]);
model.component('comp1').probe('point1').set('table', 'tbl1');
model.component('comp1').probe('point1').set('window', 'window1');

model.study.create('std1');
model.study('std1').create('stat', 'Stationary');

model.sol.create('sol1');
model.sol('sol1').study('std1');
model.sol('sol1').attach('std1');
model.sol('sol1').create('st1', 'StudyStep');
model.sol('sol1').create('v1', 'Variables');
model.sol('sol1').create('s1', 'Stationary');
model.sol('sol1').feature('s1').create('fc1', 'FullyCoupled');
model.sol('sol1').feature('s1').feature.remove('fcDef');

model.result.dataset.create('dset2', 'Solution');
model.result.dataset.create('min1', 'Minimum');
model.result.dataset('dset2').set('probetag', 'point1');
model.result.dataset('min1').set('probetag', 'point1');
model.result.dataset('min1').set('data', 'dset2');
model.result.dataset('min1').selection.geom('geom1', 0);
model.result.dataset('min1').selection.set([13]);
model.result.numerical.create('pev1', 'EvalPoint');
model.result.numerical('pev1').set('probetag', 'point1');
model.result.create('pg1', 'PlotGroup2D');
model.result.create('pg2', 'PlotGroup1D');
model.result('pg1').create('surf1', 'Surface');
model.result('pg1').feature('surf1').set('expr', 'u');
model.result('pg1').feature('surf1').create('def', 'Deform');
model.result('pg1').feature('surf1').create('filt1', 'Filter');
model.result('pg1').feature('surf1').feature('filt1').set('expr', 'solid.rho>0.05');
model.result('pg2').set('probetag', 'window1_default');
model.result('pg2').create('tblp1', 'Table');
model.result('pg2').feature('tblp1').set('probetag', 'point1');
model.result.export.create('data1', 'Data');

model.component('comp1').probe('point1').genResult([]);

model.result('pg2').tag('pg2');

model.study('std1').feature('stat').set('plot', true);

model.sol('sol1').attach('std1');
model.sol('sol1').feature('st1').label([native2unicode(hex2dec({'7f' '16'}), 'unicode')  native2unicode(hex2dec({'8b' 'd1'}), 'unicode')  native2unicode(hex2dec({'65' 'b9'}), 'unicode')  native2unicode(hex2dec({'7a' '0b'}), 'unicode') ': ' native2unicode(hex2dec({'7a' '33'}), 'unicode')  native2unicode(hex2dec({'60' '01'}), 'unicode') ]);
model.sol('sol1').feature('v1').label([native2unicode(hex2dec({'56' 'e0'}), 'unicode')  native2unicode(hex2dec({'53' 'd8'}), 'unicode')  native2unicode(hex2dec({'91' 'cf'}), 'unicode') ' 1.1']);
model.sol('sol1').feature('s1').label([native2unicode(hex2dec({'7a' '33'}), 'unicode')  native2unicode(hex2dec({'60' '01'}), 'unicode')  native2unicode(hex2dec({'6c' '42'}), 'unicode')  native2unicode(hex2dec({'89' 'e3'}), 'unicode')  native2unicode(hex2dec({'56' '68'}), 'unicode') ' 1.1']);
model.sol('sol1').feature('s1').set('plot', true);
model.sol('sol1').feature('s1').set('plotgroup', 'pg1');
model.sol('sol1').feature('s1').feature('dDef').label([native2unicode(hex2dec({'76' 'f4'}), 'unicode')  native2unicode(hex2dec({'63' 'a5'}), 'unicode') ' 1']);
model.sol('sol1').feature('s1').feature('aDef').label([native2unicode(hex2dec({'9a' 'd8'}), 'unicode')  native2unicode(hex2dec({'7e' 'a7'}), 'unicode') ' 1']);
model.sol('sol1').feature('s1').feature('aDef').set('cachepattern', true);
model.sol('sol1').feature('s1').feature('fc1').label([native2unicode(hex2dec({'51' '68'}), 'unicode')  native2unicode(hex2dec({'80' '26'}), 'unicode')  native2unicode(hex2dec({'54' '08'}), 'unicode') ' 1.1']);
model.sol('sol1').feature('s1').feature('fc1').set('dtech', 'hnlin');
model.sol('sol1').feature('s1').feature('fc1').set('rstep', 2);
model.sol('sol1').feature('s1').feature('fc1').set('maxiter', 100);
model.sol('sol1').feature('s1').feature('fc1').set('plot', true);
model.sol('sol1').runAll;

model.result.dataset('dset2').label([native2unicode(hex2dec({'63' 'a2'}), 'unicode')  native2unicode(hex2dec({'94' '88'}), 'unicode')  native2unicode(hex2dec({'89' 'e3'}), 'unicode') ' 2']);
model.result.numerical('pev1').set('const', {'solid.refpntx' '0' [native2unicode(hex2dec({'52' '9b'}), 'unicode')  native2unicode(hex2dec({'77' 'e9'}), 'unicode')  native2unicode(hex2dec({'8b' 'a1'}), 'unicode')  native2unicode(hex2dec({'7b' '97'}), 'unicode')  native2unicode(hex2dec({'53' 'c2'}), 'unicode')  native2unicode(hex2dec({'80' '03'}), 'unicode')  native2unicode(hex2dec({'70' 'b9'}), 'unicode')  native2unicode(hex2dec({'ff' '0c'}), 'unicode') 'x ' native2unicode(hex2dec({'57' '50'}), 'unicode')  native2unicode(hex2dec({'68' '07'}), 'unicode') ]; 'solid.refpnty' '0' [native2unicode(hex2dec({'52' '9b'}), 'unicode')  native2unicode(hex2dec({'77' 'e9'}), 'unicode')  native2unicode(hex2dec({'8b' 'a1'}), 'unicode')  native2unicode(hex2dec({'7b' '97'}), 'unicode')  native2unicode(hex2dec({'53' 'c2'}), 'unicode')  native2unicode(hex2dec({'80' '03'}), 'unicode')  native2unicode(hex2dec({'70' 'b9'}), 'unicode')  native2unicode(hex2dec({'ff' '0c'}), 'unicode') 'y ' native2unicode(hex2dec({'57' '50'}), 'unicode')  native2unicode(hex2dec({'68' '07'}), 'unicode') ]; 'solid.refpntz' '0' [native2unicode(hex2dec({'52' '9b'}), 'unicode')  native2unicode(hex2dec({'77' 'e9'}), 'unicode')  native2unicode(hex2dec({'8b' 'a1'}), 'unicode')  native2unicode(hex2dec({'7b' '97'}), 'unicode')  native2unicode(hex2dec({'53' 'c2'}), 'unicode')  native2unicode(hex2dec({'80' '03'}), 'unicode')  native2unicode(hex2dec({'70' 'b9'}), 'unicode')  native2unicode(hex2dec({'ff' '0c'}), 'unicode') 'z ' native2unicode(hex2dec({'57' '50'}), 'unicode')  native2unicode(hex2dec({'68' '07'}), 'unicode') ]});
model.result.numerical('pev1').setResult;
model.result('pg1').label([native2unicode(hex2dec({'5e' '94'}), 'unicode')  native2unicode(hex2dec({'52' '9b'}), 'unicode') ' (solid)']);
model.result('pg1').set('frametype', 'spatial');
model.result('pg1').set('showlegendsmaxmin', true);
model.result('pg1').set('showlegendsunit', true);
model.result('pg1').feature('surf1').set('const', {'solid.refpntx' '0' [native2unicode(hex2dec({'52' '9b'}), 'unicode')  native2unicode(hex2dec({'77' 'e9'}), 'unicode')  native2unicode(hex2dec({'8b' 'a1'}), 'unicode')  native2unicode(hex2dec({'7b' '97'}), 'unicode')  native2unicode(hex2dec({'53' 'c2'}), 'unicode')  native2unicode(hex2dec({'80' '03'}), 'unicode')  native2unicode(hex2dec({'70' 'b9'}), 'unicode')  native2unicode(hex2dec({'ff' '0c'}), 'unicode') 'x ' native2unicode(hex2dec({'57' '50'}), 'unicode')  native2unicode(hex2dec({'68' '07'}), 'unicode') ]; 'solid.refpnty' '0' [native2unicode(hex2dec({'52' '9b'}), 'unicode')  native2unicode(hex2dec({'77' 'e9'}), 'unicode')  native2unicode(hex2dec({'8b' 'a1'}), 'unicode')  native2unicode(hex2dec({'7b' '97'}), 'unicode')  native2unicode(hex2dec({'53' 'c2'}), 'unicode')  native2unicode(hex2dec({'80' '03'}), 'unicode')  native2unicode(hex2dec({'70' 'b9'}), 'unicode')  native2unicode(hex2dec({'ff' '0c'}), 'unicode') 'y ' native2unicode(hex2dec({'57' '50'}), 'unicode')  native2unicode(hex2dec({'68' '07'}), 'unicode') ]; 'solid.refpntz' '0' [native2unicode(hex2dec({'52' '9b'}), 'unicode')  native2unicode(hex2dec({'77' 'e9'}), 'unicode')  native2unicode(hex2dec({'8b' 'a1'}), 'unicode')  native2unicode(hex2dec({'7b' '97'}), 'unicode')  native2unicode(hex2dec({'53' 'c2'}), 'unicode')  native2unicode(hex2dec({'80' '03'}), 'unicode')  native2unicode(hex2dec({'70' 'b9'}), 'unicode')  native2unicode(hex2dec({'ff' '0c'}), 'unicode') 'z ' native2unicode(hex2dec({'57' '50'}), 'unicode')  native2unicode(hex2dec({'68' '07'}), 'unicode') ]});
model.result('pg1').feature('surf1').set('colortable', 'Prism');
model.result('pg1').feature('surf1').set('threshold', 'manual');
model.result('pg1').feature('surf1').set('thresholdvalue', 0.2);
model.result('pg1').feature('surf1').set('resolution', 'normal');
model.result('pg1').feature('surf1').feature('def').set('scaleactive', true);
model.result('pg2').set('xlabel', [native2unicode(hex2dec({'4f' '4d'}), 'unicode')  native2unicode(hex2dec({'79' 'fb'}), 'unicode')  native2unicode(hex2dec({'57' '3a'}), 'unicode')  native2unicode(hex2dec({'ff' '0c'}), 'unicode') 'X ' native2unicode(hex2dec({'52' '06'}), 'unicode')  native2unicode(hex2dec({'91' 'cf'}), 'unicode') ' (mm), ' native2unicode(hex2dec({'70' 'b9'}), 'unicode')  native2unicode(hex2dec({'63' 'a2'}), 'unicode')  native2unicode(hex2dec({'94' '88'}), 'unicode') ' 1']);
model.result('pg2').set('ylabel', [native2unicode(hex2dec({'4f' '4d'}), 'unicode')  native2unicode(hex2dec({'79' 'fb'}), 'unicode')  native2unicode(hex2dec({'57' '3a'}), 'unicode')  native2unicode(hex2dec({'ff' '0c'}), 'unicode') 'X ' native2unicode(hex2dec({'52' '06'}), 'unicode')  native2unicode(hex2dec({'91' 'cf'}), 'unicode') ' (mm), ' native2unicode(hex2dec({'70' 'b9'}), 'unicode')  native2unicode(hex2dec({'63' 'a2'}), 'unicode')  native2unicode(hex2dec({'94' '88'}), 'unicode') ' 1']);
model.result('pg2').set('windowtitle', [native2unicode(hex2dec({'63' 'a2'}), 'unicode')  native2unicode(hex2dec({'94' '88'}), 'unicode')  native2unicode(hex2dec({'56' 'fe'}), 'unicode')  native2unicode(hex2dec({'20' '1c'}), 'unicode') '1' native2unicode(hex2dec({'20' '1d'}), 'unicode') ]);
model.result('pg2').set('xlabelactive', false);
model.result('pg2').set('ylabelactive', false);
model.result.export('data1').set('expr', {'point1'});
model.result.export('data1').set('unit', {'mm'});
model.result.export('data1').set('descr', {[native2unicode(hex2dec({'70' 'b9'}), 'unicode')  native2unicode(hex2dec({'63' 'a2'}), 'unicode')  native2unicode(hex2dec({'94' '88'}), 'unicode') ' 1']});
model.result.export('data1').set('const', {'solid.refpntx' '0' [native2unicode(hex2dec({'52' '9b'}), 'unicode')  native2unicode(hex2dec({'77' 'e9'}), 'unicode')  native2unicode(hex2dec({'8b' 'a1'}), 'unicode')  native2unicode(hex2dec({'7b' '97'}), 'unicode')  native2unicode(hex2dec({'53' 'c2'}), 'unicode')  native2unicode(hex2dec({'80' '03'}), 'unicode')  native2unicode(hex2dec({'70' 'b9'}), 'unicode')  native2unicode(hex2dec({'ff' '0c'}), 'unicode') 'x ' native2unicode(hex2dec({'57' '50'}), 'unicode')  native2unicode(hex2dec({'68' '07'}), 'unicode') ]; 'solid.refpnty' '0' [native2unicode(hex2dec({'52' '9b'}), 'unicode')  native2unicode(hex2dec({'77' 'e9'}), 'unicode')  native2unicode(hex2dec({'8b' 'a1'}), 'unicode')  native2unicode(hex2dec({'7b' '97'}), 'unicode')  native2unicode(hex2dec({'53' 'c2'}), 'unicode')  native2unicode(hex2dec({'80' '03'}), 'unicode')  native2unicode(hex2dec({'70' 'b9'}), 'unicode')  native2unicode(hex2dec({'ff' '0c'}), 'unicode') 'y ' native2unicode(hex2dec({'57' '50'}), 'unicode')  native2unicode(hex2dec({'68' '07'}), 'unicode') ]; 'solid.refpntz' '0' [native2unicode(hex2dec({'52' '9b'}), 'unicode')  native2unicode(hex2dec({'77' 'e9'}), 'unicode')  native2unicode(hex2dec({'8b' 'a1'}), 'unicode')  native2unicode(hex2dec({'7b' '97'}), 'unicode')  native2unicode(hex2dec({'53' 'c2'}), 'unicode')  native2unicode(hex2dec({'80' '03'}), 'unicode')  native2unicode(hex2dec({'70' 'b9'}), 'unicode')  native2unicode(hex2dec({'ff' '0c'}), 'unicode') 'z ' native2unicode(hex2dec({'57' '50'}), 'unicode')  native2unicode(hex2dec({'68' '07'}), 'unicode') ]});
model.result.export('data1').set('filename', 'C:\Users\<USER>\Desktop\KF\s3\U.txt');
model.result.export('data1').set('sdim', 'global');

model.component('comp1').probe('point1').genResult('none');

model.sol('sol1').runAll;

model.result('pg1').run;
model.result.export('data1').run;
model.result('pg1').run;
model.result('pg1').feature('surf1').feature('filt1').set('expr', 'solid.rho>0.02');
model.result('pg1').run;

model.label('mx2.mph');

model.result('pg1').run;
model.result.export('data1').set('filename', 'D:\Code\optimization_algorithm\opimization_surrogate\U.txt');

model.component('comp1').func('int1').set('filename', 'D:\Code\optimization_algorithm\opimization_surrogate\x.txt');
model.component('comp1').func('int1').importData;
model.component('comp1').func('int2').set('filename', 'D:\Code\optimization_algorithm\opimization_surrogate\x.txt');
model.component('comp1').func('int2').importData;

model.result('pg1').run;

model.component('comp1').probe('point1').genResult('none');

model.sol('sol1').runAll;

model.result('pg1').run;
model.result.export('data1').set('alwaysask', false);
model.result.export('data1').set('batch', true);
model.result.export('data1').run;
model.result.export('data1').set('alwaysask', false);
model.result.export('data1').set('batch', false);

model.label('mx2.mph');

model.component('comp1').func('int1').discardData;
model.component('comp1').func('int1').importData;
model.component('comp1').func('int2').discardData;
model.component('comp1').func('int2').importData;

model.result('pg1').run;

model.component('comp1').probe('point1').genResult('none');

model.sol('sol1').runAll;

model.result('pg1').run;

model.component('comp1').func('int1').discardData;
model.component('comp1').func('int1').importData;

model.result('pg2').set('window', 'window1');
model.result('pg2').run;
model.result.export.create('data2', 'Data');
model.result.export.remove('data2');
model.result('pg2').set('window', 'window1');
model.result('pg2').run;

model.component('comp1').probe('point1').genResult('none');

model.sol('sol1').runAll;

model.result('pg1').run;

model.component('comp1').func('int1').discardData;
model.component('comp1').func('int1').set('filename', ['C:\Users\<USER>\Desktop\' native2unicode(hex2dec({'7a' '7a'}), 'unicode')  native2unicode(hex2dec({'95' 'f4'}), 'unicode')  native2unicode(hex2dec({'63' 'a7'}), 'unicode')  native2unicode(hex2dec({'52' '36'}), 'unicode')  native2unicode(hex2dec({'70' 'b9'}), 'unicode') '\Non-gradient\x.txt']);
model.component('comp1').func('int1').importData;
model.component('comp1').func('int2').discardData;
model.component('comp1').func('int2').set('filename', ['C:\Users\<USER>\Desktop\' native2unicode(hex2dec({'7a' '7a'}), 'unicode')  native2unicode(hex2dec({'95' 'f4'}), 'unicode')  native2unicode(hex2dec({'63' 'a7'}), 'unicode')  native2unicode(hex2dec({'52' '36'}), 'unicode')  native2unicode(hex2dec({'70' 'b9'}), 'unicode') '\Non-gradient\x.txt']);
model.component('comp1').func('int2').importData;

model.component('comp1').probe('point1').genResult('none');

model.component('comp1').func('int1').discardData;
model.component('comp1').func('int1').set('filename', 'D:\Code\optimization_algorithm\opimization_surrogate\x.txt');
model.component('comp1').func('int1').importData;
model.component('comp1').func('int2').discardData;
model.component('comp1').func('int2').set('filename', 'D:\Code\optimization_algorithm\opimization_surrogate\x.txt');
model.component('comp1').func('int2').importData;

model.result('pg1').run;
model.result('pg1').run;
model.result('pg1').run;

model.component('comp1').probe('point1').genResult('none');

model.sol('sol1').runAll;

model.result('pg1').run;
model.result.export('data1').set('alwaysask', false);
model.result.export('data1').set('batch', true);
model.result.export('data1').run;
model.result.export('data1').set('alwaysask', false);
model.result.export('data1').set('batch', false);
model.result('pg2').set('window', 'window1');
model.result('pg2').set('windowtitle', [native2unicode(hex2dec({'63' 'a2'}), 'unicode')  native2unicode(hex2dec({'94' '88'}), 'unicode')  native2unicode(hex2dec({'56' 'fe'}), 'unicode')  native2unicode(hex2dec({'20' '1c'}), 'unicode') '1' native2unicode(hex2dec({'20' '1d'}), 'unicode') ]);
model.result('pg2').run;

out = model;
