import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import numpy as np
from surrogate_model import kriging2
import time
from scipy import sparse
from scipy.sparse import linalg
from optimization_algorithm import XMMA
from sampling import lhs
from scipy.io import loadmat
import scipy
from pyKriging.krige import kriging
import mma
import mph
import matplotlib.pyplot as plt
import subprocess

def st(x, nelx, nely, V0, c):
    V = np.sum(x)/(nelx*nely)
    Y1 = 0
    if V > V0:
        Y1 = (V-V0)*2*10**(np.floor(np.log10(np.abs(c)+0.001))+1)
    ys = Y1
    return ys, V

def pre2(Xnew, model):
    Beta = model.Beta
    Rn = model.Rn
    X = model.X
    S = model.S
    Y = model.Y
    sita = model.sita
    diff_x = (Xnew[:, 0].reshape(-1, 1) - S[:, 0].reshape(-1, 1).T)**2
    diff_y = (Xnew[:, 1].reshape(-1, 1) - S[:, 1].reshape(-1, 1).T)**2
    r = np.exp(-sita[0, 0] * diff_x - sita[0, 1]* diff_y)
    r = r.T
    Ynew = Beta + r.T@Rn@(Y - X @ Beta)
    return Ynew

def KF(S, Y, sita, nelx, nely, xs):
    model = kriging2(S, Y, sita)
    model.get_model()
    x00 = np.empty(shape=(nelx*nely, 2))
    xx00 = np.empty(shape=(nelx*nely, 2))
    i = 0
    for ely in range(1, nely+1):
        for elx in range(1, nelx+1):
            x00[i] = np.array([elx-0.5, ely-0.5])
            xx00[i] = np.array([elx, ely])
            i += 1
    yg = pre2(x00, model)
    fyg = 1./(1+np.exp(-xs*(yg*2-1)*10))
    fyg[fyg < 0.001] = 0.001
    xg = x00
    i = 0
    x = np.empty((nely, nelx))
    for ely in range(nely):
        for elx in range(nelx):
            x[ely, elx] = fyg[i]
            i += 1
    return x, fyg, yg, xg, model


def pre20(Xnew, model, Beta, dmY):
    h, l = Xnew.shape
    Rn = model.Rn
    X = model.X
    S = model.S
    Y = dmY
    sita = model.sita
    diff_x = (Xnew[:, 0] - S[:, 0]) ** 2
    diff_y = (Xnew[:, 1] - S[:, 1]) ** 2
    r = np.exp(-sita[0, 0]*diff_x - sita[0, 1]*diff_y)
    r = r.H
    Ynew = <EMAIL>(shape=(r.shape[1], 1)) + (r.H@Rn)@(Y-X@Beta)
    return Ynew

def lk():
    E = 1
    nu = 0.3
    k = np.array(
        [1/2-nu/6, 1/8+nu/8, -1/4-nu/12, -1/8+3*nu/8,
         -1/4+nu/12, -1/8-nu/8, nu/6, 1/8-3*nu/8]
    )
    KE = E/(1-nu**2)@np.array([k[0], k[1], k[2], k[3], k[4], k[5], k[6], k[7],
                      k[1], k[0], k[7], k[6], k[5], k[4], k[3], k[2],
                      k[2], k[7], k[0], k[5], k[6], k[3], k[4], k[1],
                      k[3], k[6], k[5], k[0], k[7], k[2], k[1], k[4],
                      k[4], k[5], k[6], k[7], k[0], k[1], k[2], k[3],
                      k[5], k[4], k[3], k[2], k[1], k[0], k[7], k[6],
                      k[6], k[3], k[4], k[1], k[2], k[7], k[0], k[5],
                      k[7], k[2], k[1], k[4], k[3], k[6], k[5], k[0]])
    return KE

def zl(x0):
    data = x0
    y = data[:, 0]
    x = data[:, 1]
    values = data[:, 2]
    
    scale_factor = 0.25
    x_scaled = x*scale_factor
    y_scaled = y*scale_factor

    scaled_data = np.column_stack((y_scaled, x_scaled, values))
    array_data = []
    for i in range(4):
        for j in range(3):
            y_offset = y_scaled + j*25
            x_offset = x_scaled + i*25
            offset_data = np.column_stack((y_offset, x_offset, values))
            array_data.append(offset_data)
    return np.vstack(array_data)
    
def FE(x):
    h, l = x.shape
    wz = []
    for i in range(h):
        for j in range(l):
            wz.append([j, i, x[i, j]])
    wz = np.array(wz)
    wzl = wz[:, 2]
    wzl[wzl < 0.02] = 0.02
    wz[:, 2] = wzl
    wz0 = zl(wz)
    np.savetxt('x.txt', wz0, delimiter=',')
    # client = mph.start()
    # model = client.load('mx2.mph')
    # model.solve()
    # U = model.evaluate('u')
    # 创建一个临时的MATLAB脚本来初始化COMSOL
    matlab_script = """
    addpath('D:\comsol\COMSOL63\Multiphysics\mli');
    mphstart;
    mx2;
    exit;
    """
    # 将脚本写入临时文件，然后调用
    with open('temp_script.m', 'w') as f:
        f.write(matlab_script)
    subprocess.run(['matlab', '-batch', 'temp_script'], 
                        capture_output=True, text=True)
    # 读取U.txt文件的最后一个数值
    with open('U.txt', 'r', encoding='utf-8') as f:
        content = f.read().strip()

    # 按行分割，取最后一个非注释行
    lines = [line.strip() for line in content.split('\n') if line.strip() and not line.strip().startswith('%')]
    U = float(lines[-1])
    U = -U
    return U

def st(x, nelx, nely, V0, c):
    V = np.sum(x)/(nelx*nely)
    Y1 = 0
    if V > V0:
        Y1 = (V-V0)*2*10**(np.floor(np.log10(np.abs(c)+0.001))+1)
    ys = Y1
    return ys, V

if __name__ == "__main__":
    nelx = 100
    nely = 100
    volfrac = 0.2
    penal = 3
    N = 2
    sy = 0
    qs0 = 1
    cd = 2
    alf = 0.2
    tzs = 30
    low = 0.01
    up = 1
    jg1 = 14
    jg2 = 14
    pd1 = 0
    pdz = 2.5
    x1 = np.array(range(0, nelx, jg1))
    x2 = np.array(range(0, nely, jg2))
    x12 = 0
    S = []
    for i in range(len(x1)):
        for j in range(len(x2)):
            x12 += 1
            S.append([x1[i], x2[j]])
    S = np.array(S)
    Y = np.array([[volfrac]]*x12)
    c = 0
    sta = np.array([1/(jg1*1)**2, 1/(jg2*1)**2])
    theta = np.zeros((1,N))
    theta[0, :] = sta

    N0 = x12
    ytheta = np.array([0.2e0]*N0).reshape(1, -1)
    ylob = np.array([1e-4]*N0).reshape(1, -1)
    yupb = np.array([1e1]*N0).reshape(1, -1)
    Cy = N0*cd

    for qs in range(qs0, 12):
        Cy0 = Cy
        alf = qs*0.2
        if sy == 0:
            S0 = lhs(N0, Cy, 0, 1)
            S0[0, :] = volfrac
            if qs > 1:
                S0[0, :] = loadmat("xx0.mat")
            ylow = np.maximum(S0[0, :] - 0.2*0.95**(qs-1), low)
            yup = np.minimum(S0[0, :]+0.2*0.95**(qs-1), up)
            S0[1:, :] = S0[1:, :]*(yup-ylow) + ylow
            Y0 = []
            Vx = []
            Vv = []
            for j in range(Cy):
                Y = S0[j, :]
                print(1)
                x, fyg, yg, xg, model = KF(S, Y, theta, nelx, nely, alf)
                print(x)
                U = FE(x**penal)
                print(U)
                c = U
                ys, Vx0 = st(x, nelx, nely, volfrac, c)
                c += ys
                Y0.append(c)
                Vx.append(Vx0)
                Vv.append(ys)
            Y0 = np.array(Y0)
            Vx = np.array(Vx)
            Vv = np.array(Vv)
            scipy.io.savemat("SY0.mat", {"S0":S0, "Y0":Y0, "alf":alf})
        else:
            SY0 = loadmat("SY0.mat")
            ylow = max(S0[0, :]-0.2*0.95**(qs-1), low)
            yup = min(S0[0, :]+0.2*0.95**(qs-1), up)
            sy = 0
        dmodel = model.model
        zzt1 = 0
        pdcs = 0
        while True:
            m = 1
            n = N0
            zzt1 += 1
            loop1 = 0
            minY = np.min(Y0)
            wzp = np.where(Y0 == minY)[0]
            xval = S0[wzp, :].T
            S00 = S0[wzp, :]
            sh, sl = S00.shape
            if sh > 1: break
        change = 1
        while change > 0.0001 and loop1 < 50:
            loop1 += 1
            c = dmodel.predict(S00)
            dc = model.kriging_gradient(S00)
            df0dx = dc
            fval = np.array([1])
            dfdx = np.zeros((m, n))
            if loop1 == 1:
                a = np.zeros(m)
                cc = 10000*np.ones(m)
                d = np.zeros(m)
                a0 = 1
                xold1 = xval
                xold2 = xval
                low0 = ylow.T
                up0 = yup.T
                xmin = ylow.T
                xmax = yup.T
            xmma,ymma,zmma,lam,xsi,eta,mu,zet,s,low0,up0 = mma.mmasub(m,n,loop1,xval,xmin,xmax,xold1,xold2,c,df0dx,fval,dfdx,low0,up0,a0,a,cc,d)
            xold2 = xold1
            xold1 = xval
            xval = xmma
            S00 = xmma.T
            if loop1 > 2:
                change = np.max(np.abs(xmma-xold1))
            print(f" ite: {loop1:4d} Ob: {c:10.4f} ch: {change:10.4f}qs {qs:10.4f}")
        Shs = S00
        x, fyg, yg, xg, model = KF(S, Y, theta, nelx, nely, alf)
        U = FE(x**penal)
        c = U
        ys, Vx0 = st(x, nelx, nely, volfrac, c)
        c += ys
        Y1 = c
        Cy0 = Cy + zzt1
        Y0[Cy0] = Y1
        Vv[Cy0] = ys
        Vx[Cy0] = Vx0
        S0[Cy0] = S00

        if Y1 < np.min(Y[:-1]):
            plt.figure(1)
            plt.imshow(-x, cmap='gray', aspect='equal')
            plt.axis('off')
            plt.tight_layout()
            plt.pause(1e-6)
            plt.show()
            scipy.io.savemat('MD.mat', {'x': x})
        plt.figure(2)
        plt.plot(Y0, '-o')
        wzp = np.where(np.min(Y0) == Y1)[0]
        Vxt = Vx[wzp]
        xx0 = S0[wzp]
        scipy.io.savemat('SY.mat', {'S0': S0, 'Y0': Y0})
        scipy.io.savemat('xx0.mat', {'xx0': xx0})
        print(f" Ymin: {np.min(Y0):6.3f} V: {Vxt:6.3f} 次数: {zzt1:6.3f}圈数 {qs:10.4f}")
        if np.min(Y0[:-1]-Y0) < 0.001*min(Y0[:-1]):
            pdcs += 1
        else:
            pdcs = 0
        if pdcs > 5 or Cy0 > (Cy + tzs): break
        kriging = kriging2(S, Y, theta)
        kriging.get_model()
        dmodel = kriging.model
    
