import numpy as np
import torch
from torch import nn
from torch.nn import functional as F
import matplotlib.pyplot as plt
import utils
import random
from pyKriging.krige import kriging
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
import math

np.random.seed(123)
torch.manual_seed(123)
torch.cuda.manual_seed(123)
random.seed(123)

class Surrogate(nn.Module):
    def __init__(self, n_input, n_output, n_hiddens, n_layers,dropout=0.5):
        super().__init__()
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.n_input = n_input
        self.n_output = n_output
        self.n_hidden = n_hiddens
        self.model = nn.Sequential()
        for i in range(n_layers):
            if i == 0:
                self.model.add_module('linear'+str(i), nn.Linear(self.n_input, self.n_hidden))
                
            else:
                self.model.add_module('linear'+str(i), nn.Linear(self.n_hidden, self.n_hidden))
            self.model.add_module('dropout'+str(i), nn.Dropout(dropout))
            self.model.add_module('batchnorm'+str(i), nn.BatchNorm1d(self.n_hidden))
            self.model.add_module('relu'+str(i), nn.LeakyReLU())
        self.model.add_module('linear'+str(n_layers), nn.Linear(self.n_hidden, self.n_output))
        self.model = self.model.to(self.device)

    def forward(self, x):
        return self.model(x)

    def train(self, x_train, y_train, epochs=1000, lr=0.01, batch_size=5, patience=100, alpha=1, beta=0):
        x_train = x_train.clone().detach().float()
        y_train = y_train.clone().detach().float()
        optimizer = torch.optim.Adam(self.model.parameters(), lr=lr)
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'max', patience=patience)
        loss_fn = nn.MSELoss()
        curr = 0        
        for epoch in range(epochs):
            flag = -1
            alpha = 1/math.exp(curr)
            beta = math.log(curr+1)
            x_batch = x_train.to(self.device)
            y_batch = y_train.to(self.device)
            y_pred = self.model(x_batch).flatten()
            l2_lambda = 0.001
            l2_reg = torch.tensor(0., device=self.device)
            for param in self.model.parameters():
                l2_reg += torch.norm(param, p=2)  # L2范数（p=1则为L1）
            part1 = loss_fn(y_pred, y_batch)
            flag = max(part1, flag)
            part2 = (l2_lambda/2)*l2_reg
            diff = torch.abs(y_pred-y_batch)
            _, idx = torch.sort(diff, descending=True)
            num = int(len(diff)*0.1)
            #num = 1
            part3 = torch.sum(diff[idx[:num]])
            loss = alpha*part1+part2+beta*part3
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            scheduler.step(loss)
            if flag < 0.05:
                curr = min(curr+1, 10)
            if epoch % 100 == 0:
                print(f'Epoch: {epoch}, Loss: {loss.item()}, part1: {part1.item()}, part3: {part3.item()}')

    @torch.no_grad()
    def predict(self, x_test):
        self.model.eval()
        x_test = x_test.clone().detach().float().to(self.device)
        y_pred = self.model(x_test)
        return y_pred.cpu().detach().numpy()

    def plot_comparison(self, obj_func, train_x=None, train_y=None):
        x = np.linspace(-3, 3, 100)
        y = np.linspace(-3, 3, 100) 
        X, Y = np.meshgrid(x, y)
        pred_Z = np.zeros_like(X)
        true_Z = np.zeros_like(X)
        
        grid_points = np.column_stack((X.flatten(), Y.flatten()))
        grid_points_tensor = utils.toTensor(grid_points).to(self.device)
        
        pred_values = self.predict(grid_points_tensor)
        pred_Z = pred_values.reshape(X.shape)
        
        for i in range(X.shape[0]):
            for j in range(X.shape[1]):
                true_Z[i, j] = obj_func(np.array([[X[i, j], Y[i, j]]]))

        fig = plt.figure(figsize=(12, 6)) 
        ax1 = fig.add_subplot(121, projection='3d') 
        ax2 = fig.add_subplot(122, projection='3d') 

        surf1 = ax1.plot_surface(X, Y, pred_Z, cmap='winter',
                        linewidth=0, antialiased=False, alpha=0.8)
        ax1.contour(X, Y, pred_Z, zdir='z', offset=0.0, cmap='winter') 

        surf2 = ax2.plot_surface(X, Y, true_Z, cmap='winter', 
                        linewidth=0, antialiased=False, alpha=0.8)
        ax2.contour(X, Y, true_Z, zdir='z', offset=0.0, cmap='winter') 

        if train_x is not None and train_y is not None:
            train_x_np = utils.tondarry(train_x)
            predicted_train_y_np = self.predict(utils.toTensor(train_x_np).to(self.device)).flatten()
            z_offset_for_points = 0.05 
            ax1.scatter(train_x_np[:20, 0], train_x_np[:20, 1], 
                        predicted_train_y_np[:20] + z_offset_for_points, 
                        color='red', s=20, zorder=5, alpha=1.0) 

        # Set axis labels and titles for both subplots
        ax1.set_xlabel('X轴')
        ax1.set_ylabel('Y轴')
        ax1.set_zlabel('代理模型函数值')
        ax1.set_title('代理模型函数') 

        ax2.set_xlabel('X轴')
        ax2.set_ylabel('Y轴')
        ax2.set_zlabel('真实函数值')
        ax2.set_title('真实函数图像') 

        # ax1.set_zlim(0.0, 2.0)
        # ax2.set_zlim(0.0, 2.0)

        ax1.view_init(elev=25, azim=-125) 
        ax2.view_init(elev=25, azim=-125) 

        plt.show()
    
    def valid_model(self, x_valid, y_valid):
        x_valid = x_valid.clone().detach().float().to(self.device)
        y_valid = y_valid.clone().detach().float().to(self.device)
        y_pred = self.predict(x_valid).flatten()
        y_pred = utils.toTensor(y_pred).to(self.device)
        distance = torch.abs(y_pred - y_valid)
        max_pix = torch.argmax(distance)
        loss_value = torch.sum(distance)/torch.sum(y_valid)
        loss_value = F.mse_loss(y_pred, y_valid, reduction='mean')
        return loss_value.item(), x_valid[max_pix]


class kriging2():
    def __init__(self, S, Y, sita):
        self.nsS = S.shape[0]
        self.S = S
        self.Y = Y
        self.sita = sita
        self.model = kriging(S, Y.flatten(), name='kriging')
        self.model.train(optimizer='ga')

    def R(self):
        X1, X2 = np.meshgrid(self.S[:, 0], self.S[:, 0])
        Y1, Y2 = np.meshgrid(self.S[:, 1], self.S[:, 1])
        diff_x = (X1-X2)**2
        diff_y = (Y1-Y2)**2
        R = np.exp(-self.sita[0, 0]*diff_x - self.sita[0, 1]*diff_y)
        return R
    
    def get_model(self):
        R = self.R()
        X = np.ones((self.nsS, 1))
        Rn = np.linalg.inv(R)
        s00 = X.T@Rn
        first = s00@X
        second = s00@self.Y
        second = second.reshape(-1, 1)
        Beta = np.linalg.solve(second, first).reshape(1)
        self.Beta = Beta
        self.Rn = Rn
        self.X = X
        self.first = first
        self.second = second
        self.s00 = s00
